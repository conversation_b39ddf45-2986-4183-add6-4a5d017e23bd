
# Private env vars that should never show up in the browser
# These are used by the server to connect to Nile database
NILE_USER = 
NILE_PASSWORD =

# Client (public) env vars

# the URL of this example + where the api routes are located
# Use this to instantiate Nile context for client-side components
NEXT_PUBLIC_APP_URL=http://localhost:3000/api 

# Uncomment if you want to try Google Auth
# AUTH_TYPE=google

# for AI estimates - switch with your key, vendor url and model id
AI_API_KEY=your-ai-vendor-api-key
AI_BASE_URL=https://api.fireworks.ai/inference/v1
AI_MODEL=accounts/fireworks/models/llama-v3p1-405b-instruct
EMBEDDING_MODEL=nomic-ai/nomic-embed-text-v1.5